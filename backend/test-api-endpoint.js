#!/usr/bin/env node

/**
 * Test script to verify the /ai/analyze endpoint is working with Google AI
 * This tests the actual API endpoint that the frontend will use
 */

import axios from 'axios';

async function testAPIEndpoint() {
  console.log('🔍 Testing CodeTutor AI /ai/analyze endpoint...\n');

  const baseURL = 'http://localhost:3001';
  const endpoint = '/ai/analyze';

  try {
    // Test data
    const testProblem = "I want to add authentication to my React app using Firebase";

    console.log('📤 Sending request to:', `${baseURL}${endpoint}`);
    console.log('📝 Test problem:', testProblem);
    console.log('\n⏳ Waiting for response...\n');

    const response = await axios.post(`${baseURL}${endpoint}`, {
      problem: testProblem
    }, {
      timeout: 30000, // 30 second timeout
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 200) {
      console.log('✅ API endpoint responded successfully!');
      console.log('📊 Response status:', response.status);
      
      const data = response.data;
      
      if (data.success) {
        console.log('✅ Tutorial generation successful!');
        console.log('\n📋 Tutorial Plan Summary:');
        console.log('─'.repeat(50));
        console.log(`Title: ${data.tutorialPlan.title}`);
        console.log(`Difficulty: ${data.tutorialPlan.difficulty}`);
        console.log(`Estimated Time: ${data.tutorialPlan.estimatedTime}`);
        console.log(`Technologies: ${data.tutorialPlan.technologies.join(', ')}`);
        console.log(`Steps: ${data.tutorialPlan.steps.length} steps`);
        console.log(`Model Used: ${data.metadata.model}`);
        console.log('─'.repeat(50));

        if (data.metadata.model === 'gemini-pro') {
          console.log('\n🎉 Google AI API is working correctly!');
          console.log('✅ Real AI-generated tutorial content');
          console.log('✅ API key is valid and functional');
        } else if (data.metadata.model === 'mock') {
          console.log('\n⚠️  Using mock data (Google AI API not working)');
          console.log('❌ Check your Google AI API key configuration');
          console.log('💡 Run the test-google-api.js script for more details');
        }

        // Show first step as example
        if (data.tutorialPlan.steps.length > 0) {
          console.log('\n📝 First Step Example:');
          console.log('─'.repeat(30));
          const firstStep = data.tutorialPlan.steps[0];
          console.log(`${firstStep.stepNumber}. ${firstStep.title}`);
          console.log(`Description: ${firstStep.description}`);
          if (firstStep.codeExample) {
            console.log(`Code Example: ${firstStep.codeExample.substring(0, 100)}...`);
          }
          console.log('─'.repeat(30));
        }

      } else {
        console.log('❌ API returned success=false');
        console.log('Error:', data.error || 'Unknown error');
      }

    } else {
      console.log('❌ API endpoint returned non-200 status:', response.status);
    }

  } catch (error) {
    console.log('\n❌ API endpoint test failed!');
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔌 Connection refused - is the backend server running?');
      console.log('💡 Start the server with: npm run dev');
    } else if (error.response) {
      console.log('📊 Response status:', error.response.status);
      console.log('📝 Response data:', error.response.data);
    } else if (error.request) {
      console.log('📡 No response received from server');
      console.log('💡 Check if the server is running on port 3001');
    } else {
      console.log('❌ Error:', error.message);
    }
    
    console.log('\n💡 Troubleshooting:');
    console.log('1. Make sure the backend server is running (npm run dev)');
    console.log('2. Check that the server is listening on port 3001');
    console.log('3. Verify your Google AI API key is configured correctly');
    
    process.exit(1);
  }
}

// Test examples endpoint as well
async function testExamplesEndpoint() {
  console.log('\n🔍 Testing /ai/examples endpoint...\n');

  const baseURL = 'http://localhost:3001';
  const endpoint = '/ai/examples';

  try {
    const response = await axios.get(`${baseURL}${endpoint}`);
    
    if (response.status === 200) {
      console.log('✅ Examples endpoint working!');
      console.log(`📊 Found ${response.data.examples.length} example tutorials`);
      
      response.data.examples.forEach((example, index) => {
        console.log(`${index + 1}. ${example.title} (${example.difficulty})`);
      });
    }
  } catch (error) {
    console.log('❌ Examples endpoint failed:', error.message);
  }
}

// Run the tests
testAPIEndpoint()
  .then(() => testExamplesEndpoint())
  .catch(console.error);

export { testAPIEndpoint, testExamplesEndpoint };
