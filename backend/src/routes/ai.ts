import express from 'express'
import { GoogleGenerativeAI } from '@google/generative-ai'

const router = express.Router()

// Initialize Google AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY || '')

// Mock tutorial generation for development/demo purposes
function generateMockTutorial(problem: string) {
  const problemLower = problem.toLowerCase()

  // Determine tutorial type based on keywords
  let tutorialData = {
    title: "Custom Coding Tutorial",
    difficulty: "intermediate" as const,
    estimatedTime: "15-20 minutes",
    technologies: ["JavaScript", "React"],
    steps: [] as any[],
    prerequisites: ["Basic JavaScript knowledge", "Node.js installed"],
    resources: ["Official documentation", "Community tutorials"]
  }

  if (problemLower.includes('auth') || problemLower.includes('login') || problemLower.includes('signup')) {
    tutorialData = {
      title: "Add Authentication to React App",
      difficulty: "intermediate",
      estimatedTime: "20-25 minutes",
      technologies: ["React", "Firebase", "JavaScript"],
      steps: [
        {
          stepNumber: 1,
          title: "Set up Firebase Project",
          description: "Create a new Firebase project and enable Authentication",
          codeExample: "",
          uiActions: ["Go to Firebase Console", "Click 'Create Project'", "Enable Authentication"],
          expectedResult: "Firebase project created with Authentication enabled"
        },
        {
          stepNumber: 2,
          title: "Install Firebase SDK",
          description: "Install Firebase SDK in your React project",
          codeExample: "npm install firebase",
          uiActions: ["Open terminal", "Run npm install command"],
          expectedResult: "Firebase SDK installed in your project"
        },
        {
          stepNumber: 3,
          title: "Configure Firebase",
          description: "Set up Firebase configuration in your React app",
          codeExample: `import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  // Your config
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);`,
          uiActions: ["Create firebase.js file", "Add configuration code"],
          expectedResult: "Firebase configured and ready to use"
        },
        {
          stepNumber: 4,
          title: "Create Login Component",
          description: "Build a login form component with email/password authentication",
          codeExample: `import { signInWithEmailAndPassword } from 'firebase/auth';
import { auth } from './firebase';

const handleLogin = async (email, password) => {
  try {
    await signInWithEmailAndPassword(auth, email, password);
  } catch (error) {
    console.error('Login error:', error);
  }
};`,
          uiActions: ["Create Login.jsx", "Add form elements", "Implement login logic"],
          expectedResult: "Working login form that authenticates users"
        }
      ],
      prerequisites: ["React basics", "JavaScript ES6+", "Firebase account"],
      resources: ["Firebase Auth Docs", "React Firebase Tutorial"]
    }
  } else if (problemLower.includes('database') || problemLower.includes('api') || problemLower.includes('backend')) {
    tutorialData = {
      title: "Connect Database to Frontend",
      difficulty: "intermediate",
      estimatedTime: "25-30 minutes",
      technologies: ["React", "Node.js", "Express", "MongoDB"],
      steps: [
        {
          stepNumber: 1,
          title: "Set up Express Server",
          description: "Create a basic Express.js server with CORS enabled",
          codeExample: `const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

app.listen(3001, () => {
  console.log('Server running on port 3001');
});`,
          uiActions: ["Create server.js", "Install express and cors", "Start server"],
          expectedResult: "Express server running on localhost:3001"
        },
        {
          stepNumber: 2,
          title: "Connect to Database",
          description: "Set up MongoDB connection using Mongoose",
          codeExample: `const mongoose = require('mongoose');

mongoose.connect('mongodb://localhost:27017/myapp', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});`,
          uiActions: ["Install mongoose", "Add connection code", "Test connection"],
          expectedResult: "Successfully connected to MongoDB database"
        },
        {
          stepNumber: 3,
          title: "Create API Endpoints",
          description: "Build REST API endpoints for CRUD operations",
          codeExample: `app.get('/api/users', async (req, res) => {
  try {
    const users = await User.find();
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});`,
          uiActions: ["Create routes", "Add error handling", "Test endpoints"],
          expectedResult: "Working API endpoints that return data"
        },
        {
          stepNumber: 4,
          title: "Connect Frontend to API",
          description: "Use fetch or axios to call your API from React",
          codeExample: `const fetchUsers = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/users');
    const users = await response.json();
    setUsers(users);
  } catch (error) {
    console.error('Error fetching users:', error);
  }
};`,
          uiActions: ["Create API service", "Add data fetching", "Display data"],
          expectedResult: "Frontend displaying data from your database"
        }
      ],
      prerequisites: ["React basics", "Node.js installed", "MongoDB setup"],
      resources: ["Express.js Docs", "Mongoose Documentation", "REST API Best Practices"]
    }
  } else if (problemLower.includes('deploy') || problemLower.includes('vercel') || problemLower.includes('netlify')) {
    tutorialData = {
      title: "Deploy React App to Vercel",
      difficulty: "beginner",
      estimatedTime: "10-15 minutes",
      technologies: ["React", "Vercel", "Git"],
      steps: [
        {
          stepNumber: 1,
          title: "Prepare Your App",
          description: "Ensure your React app builds successfully",
          codeExample: "npm run build",
          uiActions: ["Run build command", "Check for errors", "Test build locally"],
          expectedResult: "Build folder created without errors"
        },
        {
          stepNumber: 2,
          title: "Push to GitHub",
          description: "Push your code to a GitHub repository",
          codeExample: `git add .
git commit -m "Ready for deployment"
git push origin main`,
          uiActions: ["Create GitHub repo", "Push code", "Verify upload"],
          expectedResult: "Code available on GitHub"
        },
        {
          stepNumber: 3,
          title: "Connect to Vercel",
          description: "Import your GitHub repository to Vercel",
          codeExample: "",
          uiActions: ["Go to vercel.com", "Sign in with GitHub", "Import repository"],
          expectedResult: "Project connected to Vercel"
        },
        {
          stepNumber: 4,
          title: "Deploy and Configure",
          description: "Deploy your app and configure any necessary settings",
          codeExample: "",
          uiActions: ["Click Deploy", "Wait for build", "Test live URL"],
          expectedResult: "Your React app is live on the internet!"
        }
      ],
      prerequisites: ["React app ready", "GitHub account", "Vercel account"],
      resources: ["Vercel Documentation", "React Deployment Guide"]
    }
  }

  return tutorialData
}

// Analyze problem and generate tutorial steps
router.post('/analyze', async (req, res) => {
  try {
    const { problem } = req.body

    if (!problem || typeof problem !== 'string') {
      return res.status(400).json({
        error: 'Problem description is required and must be a string'
      })
    }

    // Check if we have a valid API key
    const hasValidApiKey = process.env.GOOGLE_AI_API_KEY &&
                          process.env.GOOGLE_AI_API_KEY !== 'your-google-ai-api-key' &&
                          process.env.GOOGLE_AI_API_KEY !== 'AIzaSyDiDRECIUq61AG5MLv1MPVZrrPLAeSelks'

    let tutorialPlan

    if (hasValidApiKey) {
      try {
        // Get the generative model
        const model = genAI.getGenerativeModel({ model: "gemini-pro" })

        // Create a detailed prompt for tutorial generation
        const prompt = `
You are an expert coding instructor. A user has described a coding problem they need help with.
Your task is to analyze their problem and create a detailed, step-by-step tutorial plan.

User's Problem: "${problem}"

Please provide a response in the following JSON format:
{
  "title": "Brief title for the tutorial",
  "difficulty": "beginner|intermediate|advanced",
  "estimatedTime": "estimated time in minutes",
  "technologies": ["list", "of", "technologies", "involved"],
  "steps": [
    {
      "stepNumber": 1,
      "title": "Step title",
      "description": "Detailed description of what to do",
      "codeExample": "code snippet if applicable",
      "uiActions": ["list of UI actions like 'click button', 'open file', etc."],
      "expectedResult": "what the user should see after this step"
    }
  ],
  "prerequisites": ["list of things user should know/have"],
  "resources": ["helpful links or documentation"]
}

Make sure the steps are detailed enough to create an animated tutorial showing exactly what buttons to click, what code to write, and what the user should expect to see.
`

        // Generate content
        const result = await model.generateContent(prompt)
        const response = await result.response
        const text = response.text()

        // Try to parse the JSON response
        try {
          // Extract JSON from the response (in case there's extra text)
          const jsonMatch = text.match(/\{[\s\S]*\}/)
          if (jsonMatch) {
            tutorialPlan = JSON.parse(jsonMatch[0])
          } else {
            throw new Error('No JSON found in response')
          }
        } catch (parseError) {
          console.error('Failed to parse AI response as JSON:', parseError)
          // Fallback: return the raw text
          tutorialPlan = {
            title: "Custom Tutorial",
            difficulty: "intermediate",
            estimatedTime: "10-15 minutes",
            technologies: ["general"],
            steps: [
              {
                stepNumber: 1,
                title: "AI Analysis",
                description: text,
                codeExample: "",
                uiActions: [],
                expectedResult: "Understanding of the problem"
              }
            ],
            prerequisites: [],
            resources: []
          }
        }
      } catch (aiError) {
        console.error('Google AI API Error:', aiError)
        // Fall back to mock response
        tutorialPlan = generateMockTutorial(problem)
      }
    } else {
      // Use mock response when no valid API key
      console.log('Using mock tutorial generation (no valid Google AI API key)')
      tutorialPlan = generateMockTutorial(problem)
    }

    res.json({
      success: true,
      tutorialPlan,
      metadata: {
        generatedAt: new Date().toISOString(),
        model: hasValidApiKey ? "gemini-pro" : "mock",
        originalProblem: problem
      }
    })

  } catch (error) {
    console.error('AI Analysis Error:', error)
    res.status(500).json({
      error: 'Failed to analyze problem',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get tutorial examples
router.get('/examples', (req, res) => {
  const examples = [
    {
      id: 1,
      title: "Add Supabase Authentication",
      problem: "I want to add Supabase authentication to my React app",
      difficulty: "intermediate",
      estimatedTime: "15-20 minutes"
    },
    {
      id: 2,
      title: "Set up Google OAuth",
      problem: "How do I set up Google OAuth in my Next.js project",
      difficulty: "intermediate", 
      estimatedTime: "10-15 minutes"
    },
    {
      id: 3,
      title: "Connect Database to Frontend",
      problem: "I need to connect my database to my frontend",
      difficulty: "beginner",
      estimatedTime: "20-25 minutes"
    },
    {
      id: 4,
      title: "Deploy React App to Vercel",
      problem: "How do I deploy my React app to Vercel",
      difficulty: "beginner",
      estimatedTime: "5-10 minutes"
    }
  ]

  res.json({ examples })
})

export default router
